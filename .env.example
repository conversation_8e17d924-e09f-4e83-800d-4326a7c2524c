# N8N Webhook Configuration
N8N_WEBHOOK_URL=https://url

# Database Configuration (if using PostgreSQL)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/aichatorganizer

# Server Configuration
PORT=5000
NODE_ENV=development

#AWS
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
AWS_REGION=eu-central-1
SES_FROM_EMAIL=<EMAIL>

# Secret key for signing JWTs. Must match the secret key in your Django/Wagtail backend.
JWT_SECRET="your-super-secret-jwt-key"

# The full URL to redirect to on logout in production.
VITE_LOGOUT_URL="https://your-main-site.com/login"

# Database credentials
POSTGRES_USER="your_db_user"
POSTGRES_PASSWORD="your_db_password"
POSTGRES_DB="your_db_name"
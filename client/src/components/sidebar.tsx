import { useState } from "react";
import { Plus, Search, FolderPlus, Trash2, Settings, Sun, Moon, Calendar, X, LogOut, Filter, CheckSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FolderSection } from "@/components/folder-section";
import { useFolders } from "@/hooks/use-folders";
import { useChat } from "@/hooks/use-chat";
import { useTheme } from "@/hooks/use-theme";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/contexts/AuthContext";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

interface SidebarProps {
  currentSessionId: string | null;
  selectedChats: string[];
  onNewChat: () => void;
  onSelectChat: (sessionId: string) => void;
  onToggleSelect: (sessionId: string) => void;
  onSelectAll: () => void;
  onBulkDelete: () => void;
  onWebhookConfig: () => void;
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  onClose: () => void;
}

export function Sidebar({
  currentSessionId,
  selectedChats,
  onNewChat,
  onSelectChat,
  onToggleSelect,
  onSelectAll,
  onBulkDelete,
  onWebhookConfig,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onClose,
}: SidebarProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const { folders, createFolder } = useFolders();
  const { chatSessions } = useChat();
  const { theme, toggleTheme } = useTheme();
  const { user, logout } = useAuth();
  const isMobile = useIsMobile();

  const handleCreateFolder = async () => {
    const name = prompt("Enter folder name:");
    if (name) {
      await createFolder({ name });
    }
  };

  const filteredSessions = chatSessions.filter(session =>
    session.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Check if there are no chat sessions at all
  const hasNoSessions = chatSessions.length === 0;

  const folderSessions = folders.map(folder => ({
    ...folder,
    sessions: filteredSessions.filter(session => session.folderId === folder.id),
  }));

  const uncategorizedSessions = filteredSessions.filter(session => !session.folderId);

  return (
    <div className="w-80 sidebar-bg flex flex-col absolute md:relative z-10 h-full bg-background">
      {/* Header */}
      <div className="sticky top-0 z-20 bg-background border-b">
        {/* App Title */}
        <div className="flex items-center justify-center px-4 py-3 relative">
          <a
            href="https://strafrecht-online.org"
            target="_blank"
            rel="noopener noreferrer"
            className="text-lg md:text-2xl font-semibold text-gray-800 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white transition-colors duration-300 text-center"
            style={{ fontFamily: 'Corporal, system-ui, sans-serif' }}
          >
            STRAFRECHT-ONLINE.ORG
          </a>
          {isMobile && (
            <Button
              onClick={onClose}
              size="icon"
              variant="ghost"
              aria-label="Close sidebar"
              className="absolute right-4"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Unified Toolbar */}
        <div className="flex items-center justify-center px-4 py-3 border-t border-border/50 bg-muted/20 relative">
          <div className={`flex items-center gap-2 transition-transform duration-200 ${
            selectedChats.length > 0 ? '-translate-x-4' : ''
          }`}>
            <Button
              onClick={onNewChat}
              size="icon"
              variant="outline"
              className={`h-8 w-8 ${hasNoSessions ? 'animate-pulse bg-primary/20 border-primary/50' : ''}`}
              title="New chat"
              aria-label="New chat"
            >
              <Plus className="h-4 w-4" />
            </Button>
            <Button
              onClick={handleCreateFolder}
              size="icon"
              variant="outline"
              className="h-8 w-8"
              title="New folder"
              aria-label="New folder"
            >
              <FolderPlus className="h-4 w-4" />
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="h-8 w-8"
                  title="Search conversations"
                  aria-label="Search conversations"
                >
                  <Search className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent align="start" className="w-80">
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="pointer-events-none absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search conversations..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10"
                    />
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="h-8 w-8"
                  title="Filter by date"
                  aria-label="Filter by date"
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent align="start">
                <div className="space-y-2">
                  <div>
                    <label className="text-xs text-muted-foreground">From:</label>
                    <Input
                      type="date"
                      value={startDate}
                      onChange={(e) => onStartDateChange(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <label className="text-xs text-muted-foreground">To:</label>
                    <Input
                      type="date"
                      value={endDate}
                      onChange={(e) => onEndDateChange(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <Button
                    onClick={() => {
                      onStartDateChange('');
                      onEndDateChange('');
                    }}
                    size="sm"
                    variant="ghost"
                    className="w-full"
                  >
                    Clear
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
            <Button
              onClick={onSelectAll}
              size="icon"
              variant="outline"
              className="h-8 w-8"
              title="Select all conversations"
              aria-label="Select all conversations"
            >
              <CheckSquare className="h-4 w-4" />
            </Button>
          </div>

          {/* Conditional Delete Button */}
          {selectedChats.length > 0 && (
            <Button
              onClick={onBulkDelete}
              size="icon"
              className="h-8 w-8 bg-red-500 hover:bg-red-600 text-white border-red-500 hover:border-red-600 absolute right-4"
              title={`Delete ${selectedChats.length} selected conversation${selectedChats.length > 1 ? 's' : ''}`}
              aria-label={`Delete ${selectedChats.length} selected conversation${selectedChats.length > 1 ? 's' : ''}`}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>



      {/* Chat Sessions */}
      <div className="flex-1 overflow-y-auto p-4">
        {folderSessions.map((folder, index) => (
          <div key={folder.id}>
            {index > 0 && (
              <div className="my-4 relative">
                <div className="h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
              </div>
            )}
            <FolderSection
              folder={folder}
              sessions={folder.sessions}
              currentSessionId={currentSessionId}
              selectedChats={selectedChats}
              onSelectChat={onSelectChat}
              onToggleSelect={onToggleSelect}
            />
          </div>
        ))}

        {uncategorizedSessions.length > 0 && (
          <div>
            {folderSessions.length > 0 && (
              <div className="my-4 relative">
                <div className="h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
              </div>
            )}
            <FolderSection
              folder={null}
              sessions={uncategorizedSessions}
              currentSessionId={currentSessionId}
              selectedChats={selectedChats}
              onSelectChat={onSelectChat}
              onToggleSelect={onToggleSelect}
            />
          </div>
        )}
      </div>

      {/* User Profile Section */}
      <div className="sticky bottom-0 bg-background border-t border-border p-3">
        <Popover>
          <PopoverTrigger asChild>
            <div className="flex items-center gap-3 cursor-pointer hover:bg-muted p-3 rounded-lg transition-colors duration-200 border border-transparent hover:border-border">
              <Avatar className="h-10 w-10 ring-2 ring-primary/20">
                <AvatarImage src="" alt={`@${user?.username || 'user'}`} />
                <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                  {user?.username ? user.username.substring(0, 2).toUpperCase() : 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold truncate">{user?.username || 'User'}</p>
                <p className="text-xs text-muted-foreground truncate">{user?.email || '<EMAIL>'}</p>
                <div className="flex items-center gap-1 mt-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-muted-foreground">Online</span>
                </div>
              </div>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </div>
          </PopoverTrigger>
          <PopoverContent align="end" className="w-64">
            <div className="space-y-1">
              <div className="px-2 py-1.5">
                <p className="text-sm font-semibold">{user?.username || 'User'}</p>
                <p className="text-xs text-muted-foreground">{user?.email || '<EMAIL>'}</p>
                <div className="flex items-center gap-1 mt-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-muted-foreground">Online</span>
                </div>
              </div>
              <Separator />
              <Button
                onClick={toggleTheme}
                size="sm"
                variant="ghost"
                className="w-full justify-start"
              >
                {theme === "light" ? <Moon className="h-4 w-4 mr-2" /> : <Sun className="h-4 w-4 mr-2" />}
                {theme === "light" ? "Dark Theme" : "Light Theme"}
              </Button>
              <Button
                onClick={onWebhookConfig}
                size="sm"
                variant="ghost"
                className="w-full justify-start"
              >
                <Settings className="h-4 w-4 mr-2" />
                Webhook Settings
              </Button>
              <Separator />
              <Button
                onClick={logout}
                size="sm"
                variant="ghost"
                className="w-full justify-start text-red-500 hover:text-red-600"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}

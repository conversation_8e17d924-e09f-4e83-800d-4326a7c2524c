import { useState } from "react";
import { Plus, Search, FolderPlus, Trash2, Settings, Sun, Moon, Calendar, X, LogOut } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FolderSection } from "@/components/folder-section";
import { useFolders } from "@/hooks/use-folders";
import { useChat } from "@/hooks/use-chat";
import { useTheme } from "@/hooks/use-theme";
import { useIsMobile } from "@/hooks/use-mobile";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

interface SidebarProps {
  currentSessionId: string | null;
  selectedChats: string[];
  onNewChat: () => void;
  onSelectChat: (sessionId: string) => void;
  onToggleSelect: (sessionId: string) => void;
  onSelectAll: () => void;
  onBulkDelete: () => void;
  onWebhookConfig: () => void;
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  onClose: () => void;
}

export function Sidebar({
  currentSessionId,
  selectedChats,
  onNewChat,
  onSelectChat,
  onToggleSelect,
  onSelectAll,
  onBulkDelete,
  onWebhookConfig,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onClose,
}: SidebarProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const { folders, createFolder } = useFolders();
  const { chatSessions } = useChat();
  const { theme, toggleTheme } = useTheme();
  const isMobile = useIsMobile();

  const handleCreateFolder = async () => {
    const name = prompt("Enter folder name:");
    if (name) {
      await createFolder({ name });
    }
  };

  const filteredSessions = chatSessions.filter(session =>
    session.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const folderSessions = folders.map(folder => ({
    ...folder,
    sessions: filteredSessions.filter(session => session.folderId === folder.id),
  }));

  const uncategorizedSessions = filteredSessions.filter(session => !session.folderId);

  return (
    <div className="w-80 sidebar-bg flex flex-col absolute md:relative z-10 h-full bg-background">
      {/* Header & Toolbar */}
      <div className="sticky top-0 z-20 bg-background border-b">
        {/* Top app-bar */}
        <div className="flex items-center justify-between px-4 py-3">
          <a
            href="https://strafrecht-online.org"
            target="_blank"
            rel="noopener noreferrer"
            className="text-lg md:text-2xl font-semibold text-gray-800 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white transition-colors duration-300"
            style={{ fontFamily: 'Corporal, system-ui, sans-serif' }}
          >
            STRAFRECHT-ONLINE.ORG
          </a>
          <div className="flex items-center gap-2">
            <Button
              onClick={onNewChat}
              size="icon"
              variant="outline"
              className="bg-primary/90 text-primary-foreground hover:bg-primary"
              aria-label="New chat"
            >
              <Plus className="h-4 w-4" />
            </Button>
            {isMobile && (
              <Button onClick={onClose} size="icon" variant="ghost" aria-label="Close sidebar">
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Search + Date filter toolbar */}
        <div className="flex items-center gap-2 px-4 pb-3">
          <div className="relative flex-1">
            <Search className="pointer-events-none absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10"
            />
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button size="icon" variant="outline" aria-label="Filter by date">
                <Calendar className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent align="end">
              <div className="space-y-2">
                <div>
                  <label className="text-xs text-muted-foreground">From:</label>
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => onStartDateChange(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-xs text-muted-foreground">To:</label>
                  <Input
                    type="date"
                    value={endDate}
                    onChange={(e) => onEndDateChange(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <Button
                  onClick={() => {
                    onStartDateChange('');
                    onEndDateChange('');
                  }}
                  size="sm"
                  variant="ghost"
                  className="w-full"
                >
                  Clear
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Folder Actions */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-sm font-medium text-muted-foreground">Folders</h2>
          <Button
            onClick={handleCreateFolder}
            size="sm"
            variant="ghost"
            className="p-1 h-auto"
          >
            <FolderPlus className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={onSelectAll}
            size="sm"
            variant="outline"
            className="text-xs flex-1"
          >
            Select All
          </Button>
          <Button
            onClick={onBulkDelete}
            size="sm"
            variant="destructive"
            className="text-xs"
            disabled={selectedChats.length === 0}
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      </div>

      {/* Chat Sessions */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {folderSessions.map((folder) => (
          <FolderSection
            key={folder.id}
            folder={folder}
            sessions={folder.sessions}
            currentSessionId={currentSessionId}
            selectedChats={selectedChats}
            onSelectChat={onSelectChat}
            onToggleSelect={onToggleSelect}
          />
        ))}
        
        {uncategorizedSessions.length > 0 && (
          <FolderSection
            folder={null}
            sessions={uncategorizedSessions}
            currentSessionId={currentSessionId}
            selectedChats={selectedChats}
            onSelectChat={onSelectChat}
            onToggleSelect={onToggleSelect}
          />
        )}
      </div>

      {/* Bottom Settings */}
      <div className="sticky bottom-0 bg-background border-t border-border p-3">
        <Popover>
          <PopoverTrigger asChild>
            <div className="flex items-center gap-3 cursor-pointer hover:bg-muted p-2 rounded-lg">
              <Avatar className="h-9 w-9">
                <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-sm font-semibold truncate">shadcn</p>
                <p className="text-xs text-muted-foreground truncate"><EMAIL></p>
              </div>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </div>
          </PopoverTrigger>
          <PopoverContent align="end" className="w-64">
            <div className="space-y-1">
              <div className="px-2 py-1.5">
                <p className="text-sm font-semibold">shadcn</p>
                <p className="text-xs text-muted-foreground"><EMAIL></p>
              </div>
              <Separator />
              <Button
                onClick={toggleTheme}
                size="sm"
                variant="ghost"
                className="w-full justify-start"
              >
                {theme === "light" ? <Moon className="h-4 w-4 mr-2" /> : <Sun className="h-4 w-4 mr-2" />}
                {theme === "light" ? "Dark Theme" : "Light Theme"}
              </Button>
              <Button
                onClick={onWebhookConfig}
                size="sm"
                variant="ghost"
                className="w-full justify-start"
              >
                <Settings className="h-4 w-4 mr-2" />
                Webhook Settings
              </Button>
              <Separator />
              <Button
                size="sm"
                variant="ghost"
                className="w-full justify-start text-red-500 hover:text-red-600"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}

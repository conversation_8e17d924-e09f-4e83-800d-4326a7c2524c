import { useState, useRef, useEffect } from "react";
import { Send } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useIsMobile } from "@/hooks/use-mobile";
import { FeedbackButton } from "./feedback-button";

interface MessageInputProps {
  onSendMessage: (content: string) => void;
  isLoading: boolean;
  sessionId: string;
}

export function MessageInput({ onSendMessage, isLoading, sessionId }: MessageInputProps) {
  const [message, setMessage] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isMobile = useIsMobile();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSendMessage(message.trim());
      setMessage("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Mobile: Enter creates new line, desktop: Enter sends (unless Shift+Enter)
    if (isMobile) {
      // On mobile, En<PERSON> always creates new line
      return;
    } else {
      // On desktop, Enter sends message unless Shift is pressed
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSubmit(e);
      } else if (e.key === "Enter" && e.ctrlKey) {
        e.preventDefault();
        handleSubmit(e);
      }
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 128)}px`;
    }
  }, [message]);

  return (
    <div className="p-4 border-t border-border bg-background">
      <div className="max-w-4xl mx-auto">
        <form onSubmit={handleSubmit} className="relative">
          <div className="bg-card rounded-2xl shadow-lg border border-border p-4">
            <div className="flex items-end gap-3">
              <div className="flex-1">
                <Textarea
                  ref={textareaRef}
                  placeholder="Type your message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="w-full resize-none border-0 outline-none bg-transparent text-foreground placeholder:text-muted-foreground text-sm min-h-[24px] p-0 focus-visible:ring-0"
                  rows={1}
                  disabled={isLoading}
                />
              </div>
              <div className="flex items-center gap-2">
                <Button
                  type="submit"
                  size="sm"
                  disabled={!message.trim() || isLoading}
                  className="p-2"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-end mt-3 pt-3 border-t border-border">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <FeedbackButton />
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
